import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/core/utils/logger.dart';

/// Utility class for managing task-related timestamp operations
/// Provides static methods to update timestamps in TaskDetailModel via Realm database
class TaskUtils {
  // Private constructor to prevent instantiation
  TaskUtils._();

  /// Updates the photos timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskPhotosTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.modifiedTimeStampPhotos = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated photos timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating photos timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the signature timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskSignatureTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.modifiedTimeStampSignatures = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated signature timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating signature timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the documents timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskDocumentsTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.modifiedTimeStampDocuments = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated documents timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating documents timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the forms timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskFormsTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.modifiedTimeStampForms = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated forms timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating forms timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the members timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskMembersTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.modifiedTimeStampMembers = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated members timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating members timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the task main timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskMainTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.modifiedTimeStampTask = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated main task timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating main task timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the signature types timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskSignatureTypesTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.modifiedTimeStampSignaturetypes = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated signature types timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating signature types timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the photo types timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskPhotoTypesTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.modifiedTimeStampPhototypes = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated photo types timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating photo types timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the task commencement timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskCommencementTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.taskCommencementTimeStamp = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated commencement timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating commencement timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the task stopped timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskStoppedTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.taskStoppedTimeStamp = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated stopped timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating stopped timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates the submission timestamp for a specific task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamp]: The new timestamp to set (defaults to current time if null)
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateTaskSubmissionTimestamp(
    String taskId, [
    DateTime? timestamp,
  ]) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.submissionTimeStamp = timestamp ?? DateTime.now();
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated submission timestamp for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating submission timestamp for task $taskId: $e');
      return false;
    }
  }

  /// Updates multiple timestamps at once for efficiency
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to update
  /// - [timestamps]: Map of timestamp field names to DateTime values
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> updateMultipleTimestamps(
    String taskId,
    Map<String, DateTime?> timestamps,
  ) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        timestamps.forEach((field, timestamp) {
          final value = timestamp ?? DateTime.now();
          switch (field) {
            case 'modifiedTimeStampPhotos':
              task.modifiedTimeStampPhotos = value;
              break;
            case 'modifiedTimeStampSignatures':
              task.modifiedTimeStampSignatures = value;
              break;
            case 'modifiedTimeStampDocuments':
              task.modifiedTimeStampDocuments = value;
              break;
            case 'modifiedTimeStampForms':
              task.modifiedTimeStampForms = value;
              break;
            case 'modifiedTimeStampMembers':
              task.modifiedTimeStampMembers = value;
              break;
            case 'modifiedTimeStampTask':
              task.modifiedTimeStampTask = value;
              break;
            case 'modifiedTimeStampSignaturetypes':
              task.modifiedTimeStampSignaturetypes = value;
              break;
            case 'modifiedTimeStampPhototypes':
              task.modifiedTimeStampPhototypes = value;
              break;
            case 'taskCommencementTimeStamp':
              task.taskCommencementTimeStamp = value;
              break;
            case 'taskStoppedTimeStamp':
              task.taskStoppedTimeStamp = value;
              break;
            case 'submissionTimeStamp':
              task.submissionTimeStamp = value;
              break;
            default:
              logger('Unknown timestamp field: $field');
          }
        });
        task.isSynced = false; // Mark as needing sync
      });

      logger('Successfully updated multiple timestamps for task $taskId');
      return true;
    } catch (e) {
      logger('Error updating multiple timestamps for task $taskId: $e');
      return false;
    }
  }

  /// Retrieves a task from the database by ID
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to retrieve
  ///
  /// Returns the TaskDetailModel if found, null otherwise
  static TaskDetailModel? getTask(String taskId) {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return null;
      }

      return realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;
    } catch (e) {
      logger('Error retrieving task $taskId: $e');
      return null;
    }
  }

  /// Marks a task as sync pending
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to mark as sync pending
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> markTaskAsSyncPending(String taskId) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.syncPending = true;
        task.isSynced = false; // Mark as not synced as well
      });

      logger('Successfully marked task $taskId as sync pending');
      return true;
    } catch (e) {
      logger('Error marking task $taskId as sync pending: $e');
      return false;
    }
  }

  /// Clears the sync pending status of a task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task to clear sync pending status
  ///
  /// Returns true if update was successful, false otherwise
  static Future<bool> clearTaskSyncPending(String taskId) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return false;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return false;
      }

      realm.write(() {
        task.syncPending = false;
      });

      logger('Successfully cleared sync pending status for task $taskId');
      return true;
    } catch (e) {
      logger('Error clearing sync pending status for task $taskId: $e');
      return false;
    }
  }

  /// Gets all tasks that have sync pending status
  ///
  /// Returns a list of TaskDetailModel with syncPending = true
  static List<TaskDetailModel> getTasksWithSyncPending() {
    try {
      final realm = RealmDatabase.instance.realm;
      return realm.query<TaskDetailModel>('syncPending == true').toList();
    } catch (e) {
      logger('Error getting tasks with sync pending: $e');
      return [];
    }
  }

  /// Gets the current timestamp for a specific field in a task
  ///
  /// Parameters:
  /// - [taskId]: The ID of the task
  /// - [field]: The timestamp field name
  ///
  /// Returns the DateTime value if found, null otherwise
  static DateTime? getTaskTimestamp(String taskId, String field) {
    try {
      final task = getTask(taskId);
      if (task == null) return null;

      switch (field) {
        case 'modifiedTimeStampPhotos':
          return task.modifiedTimeStampPhotos;
        case 'modifiedTimeStampSignatures':
          return task.modifiedTimeStampSignatures;
        case 'modifiedTimeStampDocuments':
          return task.modifiedTimeStampDocuments;
        case 'modifiedTimeStampForms':
          return task.modifiedTimeStampForms;
        case 'modifiedTimeStampMembers':
          return task.modifiedTimeStampMembers;
        case 'modifiedTimeStampTask':
          return task.modifiedTimeStampTask;
        case 'modifiedTimeStampSignaturetypes':
          return task.modifiedTimeStampSignaturetypes;
        case 'modifiedTimeStampPhototypes':
          return task.modifiedTimeStampPhototypes;
        case 'taskCommencementTimeStamp':
          return task.taskCommencementTimeStamp;
        case 'taskStoppedTimeStamp':
          return task.taskStoppedTimeStamp;
        case 'submissionTimeStamp':
          return task.submissionTimeStamp;
        case 'scheduledTimeStamp':
          return task.scheduledTimeStamp;
        case 'expires':
          return task.expires;
        case 'rangeStart':
          return task.rangeStart;
        case 'rangeEnd':
          return task.rangeEnd;
        default:
          logger('Unknown timestamp field: $field');
          return null;
      }
    } catch (e) {
      logger('Error getting timestamp for task $taskId, field $field: $e');
      return null;
    }
  }
}
