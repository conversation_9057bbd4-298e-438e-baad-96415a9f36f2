import 'dart:convert';
import 'dart:io';

import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/photo_utils.dart';
import 'package:storetrack_app/core/utils/signature_utils.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_sign_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_signature_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/sync_pic_request_entity.dart'
    as sync_pic;
import 'package:storetrack_app/features/home/<USER>/entities/sync_sign_request_entity.dart'
    as sync_sign;
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart'
    as submit_report;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/di/service_locator.dart';

/// Utility class for sync-related operations
///
/// This class provides helper methods for creating API request entities
/// and processing data transformations needed for the four new sync endpoints:
///
/// 1. `/api/send_task_pic_v4_11` - Upload individual photos
/// 2. `/api/sync_pic_info_mpt` - Sync photo metadata after all uploads
/// 3. `/api/send_task_sig_v4_11` - Upload individual signatures
/// 4. `/api/sync_sig_info` - Sync signature metadata after all uploads
///
/// Usage example:
/// ```dart
/// // 1. Upload individual photos first
/// final photosToUpload = SyncUtils.getPhotosToUpload(tasks);
/// for (final photoData in photosToUpload) {
///   final request = await SyncUtils.createUploadPhotoRequest(
///     photo: photoData['photo'],
///     taskId: photoData['taskId'],
///     folderId: photoData['folderId'],
///   );
///   final result = await homeRepository.uploadPhoto(request);
/// }
///
/// // 2. Then sync photo metadata
/// final syncRequest = await SyncUtils.createSyncPicInfoRequest(tasks: tasks);
/// final syncResult = await homeRepository.syncPhotoInfo(syncRequest);
/// ```
class SyncUtils {
  static const String deviceUid = "8b7a6774c878a206";

  /// Encode file to base64 string
  static Future<String> encodeFileToBase64(String filePath) async {
    final file = File(filePath);
    final bytes = await file.readAsBytes();
    final base64String = base64Encode(bytes);
    return base64String;
  }

  /// Create upload photo request entity from photo data
  static Future<UploadPhotoRequestEntity> createUploadPhotoRequest({
    required Photo photo,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadPhotoRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.photoId = photo.photoId?.toInt();
    request.photoDate = null;
    request.photoCaption = photo.caption;
    request.cannotUploadMandatory = photo.cannotUploadMandatory;
    request.formId = photo.formId?.toInt();
    request.questionId = photo.questionId?.toInt();
    request.measurementId = photo.measurementId?.toInt();
    request.questionpartId = photo.questionpartId?.toInt();
    request.questionPartMultiId = photo.questionPartMultiId;
    request.measurementPhototypeId = photo.measurementPhototypeId?.toInt();
    request.deviceuid = deviceUid;

    if (photo.cannotUploadMandatory == true) {
      request.pictureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use photoUrl
      final imagePath = photo.localPath ?? photo.photoUrl ?? "";
      request.pictureBlob = await encodeFileToBase64(imagePath);
    }

    return request;
  }

  /// Create upload signature request entity from signature data
  static Future<UploadSignRequestEntity> createUploadSignatureRequest({
    required Signature signature,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadSignRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.signatureId = signature.signatureId?.toInt();
    request.signedBy = signature.signedBy;
    request.cannotUploadMandatory = signature.cannotUploadMandatory;
    request.formId = signature.formId?.toInt();
    request.questionId = signature.questionId?.toInt();
    request.deviceuid = deviceUid;

    if (signature.cannotUploadMandatory == true) {
      request.signatureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use signatureUrl
      final signaturePath = signature.localPath ?? signature.signatureUrl ?? "";
      request.signatureBlob = await encodeFileToBase64(signaturePath);
    }

    return request;
  }

  /// Create sync pic info request entity for photo metadata sync
  static Future<sync_pic.SyncPicInfoRequestEntity> createSyncPicInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncPicRequest = sync_pic.SyncPicInfoRequestEntity();
    final List<sync_pic.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_pic.PhotoFolder> photoFolderList = [];

      for (var photoFolder in task.photoFolder ?? []) {
        final deletePhotosIds = <int>[];

        for (var photo in photoFolder.photos ?? []) {
          if (photo.userDeletedPhoto == true) {
            deletePhotosIds.add(photo.photoId?.toInt() ?? 0);
          }
        }

        if (deletePhotosIds.isNotEmpty) {
          photoFolderList.add(sync_pic.PhotoFolder(
            folderId: photoFolder.folderId?.toInt(),
            deletePhotosIds: deletePhotosIds.join(","),
          ));
        }
      }

      if (photoFolderList.isNotEmpty) {
        tasksSync.add(sync_pic.Task(
          taskId: task.taskId?.toInt(),
          uploadPhotosSuccess: true,
          modifiedTimeStampPhotos: task.modifiedTimeStampPhotos,
          photoFolder: photoFolderList,
        ));
      }
    }

    syncPicRequest.tasks = tasksSync;
    syncPicRequest.token = await dataManager.getAuthToken();
    syncPicRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncPicRequest.deviceuid = deviceUid;

    return syncPicRequest;
  }

  /// Create sync signature info request entity for signature metadata sync
  static Future<sync_sign.SyncSignInfoRequestEntity>
      createSyncSignatureInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncSignRequest = sync_sign.SyncSignInfoRequestEntity();
    final List<sync_sign.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_sign.SignatureFolder> signatureFolderList = [];

      for (var signatureFolder in task.signatureFolder ?? []) {
        final deleteSignatureIds = <int>[];

        for (var signature in signatureFolder.signatures ?? []) {
          if (signature.userDeletedSignature == true) {
            deleteSignatureIds.add(signature.signatureId?.toInt() ?? 0);
          }
        }

        if (deleteSignatureIds.isNotEmpty) {
          signatureFolderList.add(sync_sign.SignatureFolder(
            folderId: signatureFolder.folderId?.toInt(),
            deleteSignaturesIds: deleteSignatureIds.join(","),
          ));
        }
      }

      if (signatureFolderList.isNotEmpty) {
        tasksSync.add(sync_sign.Task(
          taskId: task.taskId?.toInt(),
          uploadSignatureSuccess: true,
          modifiedTimeStampSignatures: task.modifiedTimeStampSignatures,
          signatureFolder: signatureFolderList,
        ));
      }
    }

    syncSignRequest.tasks = tasksSync;
    syncSignRequest.token = await dataManager.getAuthToken();
    syncSignRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncSignRequest.deviceuid = deviceUid;

    return syncSignRequest;
  }

  /// Get all photos that need to be uploaded from tasks
  static List<Map<String, dynamic>> getPhotosToUpload(List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> photosToUpload = [];

    for (var task in tasks) {
      for (var photoFolder in task.photoFolder ?? []) {
        for (var photo in photoFolder.photos ?? []) {
          // Only include photos that haven't been deleted and have content
          if (photo.userDeletedPhoto != true &&
              (photo.photoUrl != null || photo.localPath != null)) {
            photosToUpload.add({
              'photo': photo,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': photoFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return photosToUpload;
  }

  /// Get all signatures that need to be uploaded from tasks
  static List<Map<String, dynamic>> getSignaturesToUpload(
      List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> signaturesToUpload = [];

    for (var task in tasks) {
      for (var signatureFolder in task.signatureFolder ?? []) {
        for (var signature in signatureFolder.signatures ?? []) {
          // Only include signatures that haven't been deleted and have content
          if (signature.userDeletedSignature != true &&
              (signature.signatureUrl != null || signature.localPath != null)) {
            signaturesToUpload.add({
              'signature': signature,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': signatureFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return signaturesToUpload;
  }

  /// Check if a given `photoId` already exists inside any of the photo folders
  /// of the provided [task].
  ///
  /// The API that handles `/api/send_task_pic_v4_11` returns the definitive
  /// `photoId` generated by the server.  When the same image is uploaded twice
  /// (user selects the same image again, or device retries a previous upload),
  /// the backend may respond with an **existing** `photoId` instead of a newly
  /// created one.  This helper makes it easy to detect that situation so that
  /// callers can decide to ignore the duplicate or clean up the temporary
  /// local record.
  ///
  /// Returns `true` if any photo inside `task.photoFolder` has `photoId` equal
  /// to [serverPhotoId]; otherwise returns `false`.
  static bool isDuplicatePhoto({
    required TaskDetail task,
    required int serverPhotoId,
  }) {
    if (serverPhotoId == 0) return false;

    for (final photoFolder in task.photoFolder ?? []) {
      for (final photo in photoFolder.photos ?? []) {
        if ((photo.photoId ?? 0).toInt() == serverPhotoId) {
          return true;
        }
      }
    }
    return false;
  }

  /// Handles photo upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from photo upload API and:
  /// 1. Checks if the uploaded photo is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate photo record from database
  /// 3. If not duplicate: Updates the local photo record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the photo upload API
  /// - [taskId]: The ID of the task containing the photo
  /// - [originalLocalPath]: The original local path of the uploaded photo (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handlePhotoUploadResponse({
    required UploadPhotoResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverPhoto = uploadResponse.data;
      if (serverPhoto == null) {
        logger('Invalid server photo response: data is null');
        return false;
      }

      final serverPhotoId = serverPhoto.photoId?.toInt() ?? 0;

      if (serverPhotoId == 0) {
        logger('Invalid server photo ID received');
        return false;
      }

      // Check for duplicate photo using PhotoUtils
      final isDuplicate = await PhotoUtils.photoExists(
        taskId: taskId,
        photoId: serverPhotoId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate photo record
        logger(
            'Duplicate photo detected with ID: $serverPhotoId, deleting local record');
        return await PhotoUtils.deletePhotoRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update photo record and delete local file
        logger('Updating photo record with server data for ID: $serverPhotoId');
        return await PhotoUtils.updatePhotoWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          photoId: serverPhotoId,
          photoUrl: serverPhoto.photoUrl,
          caption: serverPhoto.photoCaption,
          cannotUploadMandatory: serverPhoto.cannotUploadMandatory,
          modifiedTimeStamp: serverPhoto.modifiedTimeStampPhoto,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling photo upload response: $e');
      return false;
    }
  }

  /// Handles signature upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from signature upload API and:
  /// 1. Checks if the uploaded signature is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate signature record from database
  /// 3. If not duplicate: Updates the local signature record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the signature upload API
  /// - [taskId]: The ID of the task containing the signature
  /// - [originalLocalPath]: The original local path of the uploaded signature (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handleSignatureUploadResponse({
    required UploadSignatureResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverSignature = uploadResponse.data;
      if (serverSignature == null) {
        logger('Invalid server signature response: data is null');
        return false;
      }

      final serverSignatureId = serverSignature.signatureId?.toInt() ?? 0;

      if (serverSignatureId == 0) {
        logger('Invalid server signature ID received');
        return false;
      }

      // Check for duplicate signature using SignatureUtils
      final isDuplicate = await SignatureUtils.signatureExists(
        taskId: taskId,
        signatureId: serverSignatureId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate signature record
        logger(
            'Duplicate signature detected with ID: $serverSignatureId, deleting local record');
        return await SignatureUtils.deleteSignatureRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update signature record and delete local file
        logger(
            'Updating signature record with server data for ID: $serverSignatureId');
        return await SignatureUtils.updateSignatureWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          signatureId: serverSignatureId,
          signatureUrl: serverSignature.signatureUrl,
          signedBy: serverSignature.signedBy,
          cannotUploadMandatory: serverSignature.cannotUploadMandatory,
          modifiedTimeStamp: serverSignature.modifiedTimeStampSignature,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling signature upload response: $e');
      return false;
    }
  }

  /// Create submit report request entity from task data
  ///
  /// This method prepares the request body for the submit report API,
  /// taking inspiration from the submitReportData() method in syncing.dart.
  /// It structures the request with all necessary task data including forms,
  /// question answers, and followup tasks.
  ///
  /// Parameters:
  /// - [task]: The task detail containing all data to be submitted
  ///
  /// Returns a properly structured SubmitReportRequestEntity
  static Future<submit_report.SubmitReportRequestEntity>
      createSubmitReportRequest({
    required TaskDetail task,
  }) async {
    final dataManager = sl<DataManager>();
    const String actualDeviceUid = "8b7a6774c878a206";
    const String actualAppVersion = "9.9.9";

    final request = submit_report.SubmitReportRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = await dataManager.getUserId();
    request.deviceUid = actualDeviceUid;
    request.appversion = actualAppVersion;
    request.taskId = task.taskId.toString();
    request.comment = task.comment;
    request.minutes = task.minutes?.toInt();
    request.timerMinutes = 0;
    request.claimableKms = task.claimableKms?.toInt();
    request.pages = task.pages?.toInt();
    request.taskStatus = task.taskStatus;
    request.submissionState = task.submissionState?.toInt();
    request.taskCommencementTimeStamp = task.taskCommencementTimeStamp;
    request.taskStoppedTimeStamp = task.taskStoppedTimeStamp;
    request.scheduledTimeStamp = task.scheduledTimeStamp;
    request.submissionTimeStamp = DateTime.now();
    request.startTaskLatitude = task.taskLatitude?.toInt();
    request.startTaskLongitude = task.taskLongitude?.toInt();
    request.taskLatitude = task.latitude?.toInt();
    request.taskLongitude = task.longitude?.toInt();
    request.budgetCalculated = 0;

    // Initialize lists
    request.forms = [];
    request.followupTasks = [];
    request.resumePauseItems = [];

    // Process forms and question answers
    for (var form in task.forms ?? []) {
      var formPost = Form();
      formPost.formId = form.formId;
      formPost.questionAnswers = [];

      for (var questionAnswer in form.questionAnswers ?? []) {
        if ((questionAnswer.isComment == false) ||
            (questionAnswer.isComment == true &&
                questionAnswer.measurementTextResult != null)) {
          var questionAnswerPost = QuestionAnswer(
            questionId: questionAnswer.questionId,
            questionpartId: questionAnswer.questionpartId,
            questionPartMultiId: questionAnswer.questionPartMultiId,
            measurementId: questionAnswer.measurementId,
            measurementTypeId: questionAnswer.measurementTypeId,
            measurementOptionId: questionAnswer.measurementOptionId,
            measurementOptionIds: questionAnswer.measurementOptionIds,
            measurementTextResult: questionAnswer.measurementTextResult,
            isComment: questionAnswer.isComment,
            commentTypeId: questionAnswer.commentTypeId,
          );
          formPost.questionAnswers?.add(questionAnswerPost);
        }
      }

      if (formPost.questionAnswers?.isNotEmpty == true) {
        request.forms?.add(formPost);
      }
    }

    // Process followup tasks
    for (var followupTask in task.followupTasks ?? []) {
      var followupTaskPost = submit_report.FollowupTask();
      followupTaskPost.taskId = task.taskId.toString();
      followupTaskPost.visitDate = followupTask.selectedVisitDate;
      followupTaskPost.followupTypeId = 0;
      followupTaskPost.followupItemId = 0;
      followupTaskPost.budget = followupTask.selectedBudget?.toInt();
      followupTaskPost.scheduleNote = followupTask.selectedScheduleNote;
      followupTaskPost.followupNumber = followupTask.followupNumber?.toInt();
      request.followupTasks?.add(followupTaskPost);
    }

    return request;
  }

  /// Get all tasks that need to be submitted (have sync pending status)
  ///
  /// Returns a list of TaskDetail entities that are marked for sync
  static List<TaskDetail> getTasksToSubmit(List<TaskDetail> tasks) {
    return tasks.where((task) => task.syncPending == true).toList();
  }
}
