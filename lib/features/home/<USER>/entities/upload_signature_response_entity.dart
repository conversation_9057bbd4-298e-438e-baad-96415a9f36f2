import 'dart:convert';

class UploadSignatureResponseEntity {
    Data? data;

    UploadSignatureResponseEntity({
        this.data,
    });

    factory UploadSignatureResponseEntity.fromRawJson(String str) => UploadSignatureResponseEntity.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory UploadSignatureResponseEntity.fromJson(Map<String, dynamic> json) => UploadSignatureResponseEntity(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
    );

    Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
    };
}

class Data {
    int? taskId;
    int? folderId;
    int? signatureId;
    String? signatureUrl;
    String? signedBy;
    DateTime? modifiedTimeStampSignature;
    int? formId;
    int? questionId;
    bool? cannotUploadMandatory;

    Data({
        this.taskId,
        this.folderId,
        this.signatureId,
        this.signatureUrl,
        this.signedBy,
        this.modifiedTimeStampSignature,
        this.formId,
        this.questionId,
        this.cannotUploadMandatory,
    });

    factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

    String toRawJson() => json.encode(toJson());

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        taskId: json["task_id"],
        folderId: json["folder_id"],
        signatureId: json["signature_id"],
        signatureUrl: json["signature_url"],
        signedBy: json["signed_by"],
        modifiedTimeStampSignature: json["modified_time_stamp_signature"] == null ? null : DateTime.parse(json["modified_time_stamp_signature"]),
        formId: json["form_id"],
        questionId: json["question_id"],
        cannotUploadMandatory: json["cannot_upload_mandatory"],
    );

    Map<String, dynamic> toJson() => {
        "task_id": taskId,
        "folder_id": folderId,
        "signature_id": signatureId,
        "signature_url": signatureUrl,
        "signed_by": signedBy,
        "modified_time_stamp_signature": modifiedTimeStampSignature?.toIso8601String(),
        "form_id": formId,
        "question_id": questionId,
        "cannot_upload_mandatory": cannotUploadMandatory,
    };
}
